version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: spheroseg-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/alerts:/etc/prometheus/alerts:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - spheroseg-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.1.5
    container_name: spheroseg-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://localhost:3001
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
      - grafana-data:/var/lib/grafana
    ports:
      - "3001:3000"
    networks:
      - spheroseg-network
    restart: unless-stopped
    depends_on:
      - prometheus

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: spheroseg-alertmanager
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'
    volumes:
      - ./alertmanager/config.yml:/etc/alertmanager/config.yml:ro
      - alertmanager-data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - spheroseg-network
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: spheroseg-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - spheroseg-network
    restart: unless-stopped
    privileged: true

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: spheroseg-postgres-exporter
    environment:
      DATA_SOURCE_NAME: "**************************************/spheroseg?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - spheroseg-network
    restart: unless-stopped
    depends_on:
      - db

volumes:
  prometheus-data:
  grafana-data:
  alertmanager-data:

networks:
  spheroseg-network:
    external: true
    name: spheroseg_default