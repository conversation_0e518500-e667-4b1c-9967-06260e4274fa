global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'spheroseg-monitor'

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "alerts/*.yml"

scrape_configs:
  # Spheroseg Backend Service
  - job_name: 'spheroseg-backend'
    static_configs:
      - targets: ['backend:5001']
    metrics_path: '/api/metrics'
    scrape_interval: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Docker containers
  - job_name: 'docker-containers'
    static_configs:
      - targets: ['localhost:9323']
    relabel_configs:
      - source_labels: [__name__]
        regex: 'container_.*'
        action: keep

  # Redis Exporter (if Redis is used)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'