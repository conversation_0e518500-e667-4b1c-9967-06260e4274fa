<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SpheroSeg - Development Mode</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: #f8fafc;
      color: #1e293b;
      margin: 0;
      padding: 0;
      line-height: 1.5;
    }
    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 2rem;
      text-align: center;
    }
    .content {
      max-width: 64rem;
      margin: 0 auto;
    }
    .badge {
      display: inline-block;
      background-color: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(10px);
      padding: 0.5rem 1rem;
      border-radius: 9999px;
      margin-bottom: 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: #2563EB;
    }
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
    }
    p {
      font-size: 1.125rem;
      max-width: 36rem;
      margin: 0 auto 2rem;
      color: #64748b;
    }
    .button-group {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 3rem;
      flex-wrap: wrap;
    }
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
      text-decoration: none;
      transition: background-color 0.2s ease;
    }
    .button-primary {
      background-color: #2563EB;
      color: white;
    }
    .button-primary:hover {
      background-color: #1d4ed8;
    }
    .status-card {
      padding: 2rem;
      background-color: rgba(255, 255, 255, 0.7);
      border-radius: 1rem;
      margin-top: 2rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    h2 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
      <div class="badge">Development Mode</div>
      
      <h1>SpheroSeg - AI-Powered Cell Segmentation</h1>
      
      <p>
        Precise cell segmentation, powerful analysis tools, and seamless workflow integration for biomedical research and discovery.
      </p>
      
      <div class="button-group">
        <a href="/api/status" class="button button-primary">Check API Status</a>
      </div>
      
      <div class="status-card">
        <h2>Development Server Status</h2>
        <p>The Vite development server is experiencing issues and will be restarted automatically.</p>
        <p>This static page is being shown while the development server is restarting.</p>
        <p>Try refreshing the page in a few moments.</p>
      </div>
    </div>
  </div>
</body>
</html>