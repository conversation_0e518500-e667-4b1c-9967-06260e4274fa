{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "upload": "Upload", "download": "Download", "export": "Export", "import": "Import", "yes": "Yes", "no": "No", "ok": "OK", "confirm": "Confirm", "retry": "Retry", "imageNotFound": "Image not found", "returnToProject": "Return to project", "uploadImages": "Upload Images", "maxFileSize": "Max file size: {{size}}MB", "accepted": "Accepted", "files": "Files", "removeAll": "Remove All"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "logoutError": "Logout failed", "registerSuccess": "Registration successful", "registerError": "Registration failed", "passwordResetSuccess": "Password reset successful", "passwordResetError": "Password reset failed"}, "project": {"title": "Project", "projects": "Projects", "createProject": "Create Project", "editProject": "Edit Project", "deleteProject": "Delete Project", "projectName": "Project Name", "projectDescription": "Project Description", "projectCreatedAt": "Created At", "projectUpdatedAt": "Updated At", "projectOwner": "Owner", "projectMembers": "Members", "projectSettings": "Settings", "projectImages": "Images", "projectSegmentations": "Segmentations", "projectExports": "Exports", "projectImports": "Imports", "projectStats": "Statistics", "projectActivity": "Activity", "projectLogs": "Logs", "projectTasks": "Tasks", "projectQueue": "Queue", "projectStatus": "Status", "projectProgress": "Progress", "projectCompletion": "Completion", "projectDuration": "Duration", "projectDeadline": "Deadline", "projectPriority": "Priority", "projectTags": "Tags", "projectCategory": "Category", "projectType": "Type", "projectVisibility": "Visibility", "projectAccess": "Access", "projectPermissions": "Permissions", "projectRoles": "Roles", "projectInvitations": "Invitations", "projectRequests": "Requests", "projectNotifications": "Notifications", "projectAlerts": "<PERSON><PERSON><PERSON>", "projectWarnings": "Warnings", "projectErrors": "Errors", "projectSuccess": "Success", "projectInfo": "Info", "projectHelp": "Help", "projectSupport": "Support", "projectFeedback": "<PERSON><PERSON><PERSON>", "projectRating": "Rating", "projectReviews": "Reviews", "projectComments": "Comments", "projectNotes": "Notes", "projectDocumentation": "Documentation", "projectGuides": "Guides", "projectTutorials": "Tutorials", "projectExamples": "Examples", "projectTemplates": "Templates", "projectPresets": "Presets", "projectDefaults": "De<PERSON>ults", "projectCustomization": "Customization", "projectThemes": "Themes", "projectStyles": "Styles", "projectLayouts": "Layouts", "projectViews": "Views", "projectDashboard": "Dashboard", "projectOverview": "Overview", "projectSummary": "Summary", "projectDetails": "Details", "projectHistory": "History", "projectTimeline": "Timeline", "projectCalendar": "Calendar", "projectSchedule": "Schedule", "projectPlanning": "Planning", "projectForecasting": "Forecasting", "projectPredictions": "Predictions", "projectAnalytics": "Analytics", "projectReports": "Reports", "projectCharts": "Charts", "projectGraphs": "Graphs", "projectTables": "Tables", "projectLists": "Lists", "projectGrids": "Grids", "projectCards": "Cards", "projectItems": "Items", "projectElements": "Elements", "projectComponents": "Components", "projectModules": "<PERSON><PERSON><PERSON>", "projectSections": "Sections", "projectPages": "Pages", "projectScreens": "Screens", "projectWindows": "Windows", "projectDialogs": "Dialogs", "projectModals": "Modals", "projectPopups": "Popups", "projectTooltips": "Tooltips", "projectHints": "Hints", "projectMessages": "Messages", "noImages": {"title": "No Images Yet", "description": "This project doesn't have any images yet. Upload images to get started with segmentation.", "uploadButton": "Upload Images"}, "errorLoading": "Error loading project", "resegmentImage": "Resegment Image"}, "uploader": {"generatingPreviews": "Generating previews...", "previewsGenerated": "Previews generated", "processing": "Processing...", "uploadSuccess": "Upload successful", "dragDrop": "Drag & drop images here", "uploadError": "Please select some files to upload.", "uploadingImages": "Uploading images...", "uploadErrorGeneral": "Error uploading images. Please try again.", "clickToSelect": "Click to select files", "or": "Or", "dragAndDropFiles": "drag and drop files here", "segmentAfterUploadLabel": "Segment images immediately after upload", "uploadBtn": "Upload", "imageOnly": "Images only"}, "segmentation": {"imageNotFound": "Image not found", "returnToProject": "Return to project", "editor": "Segmentation Editor", "save": "Save Segmentation", "undo": "Undo", "redo": "Redo", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetView": "Reset View", "editMode": "Edit Mode", "startingResegmentation": "Starting resegmentation...", "viewMode": "View Mode", "addPolygon": "Add Polygon", "deletePolygon": "Delete Polygon", "editPolygon": "Edit Polygon", "resegment": "Re-segment", "loading": "Loading segmentation...", "saving": "Saving segmentation...", "saveSuccess": "Segmentation saved successfully", "saveError": "Failed to save segmentation", "loadError": "Failed to load segmentation", "resegmentSuccess": "Re-segmentation triggered successfully", "resegmentError": "Failed to trigger re-segmentation", "processingImage": "Processing image...", "queue": {"statusReady": "Ready", "statusProcessing": "Processing", "statusCompleted": "Completed", "statusFailed": "Failed", "statusPending": "Pending"}}, "errors": {"permissions": {"viewImage": "You need at least 'viewer' permission to view this image", "deleteImage": "You need 'edit' or 'owner' permission to delete images", "saveSegmentation": "You need 'edit' or 'owner' permission to save segmentation results", "resegmentImage": "You need 'edit' or 'owner' permission to resegment images", "editSegmentation": "You need 'edit' or 'owner' permission to edit segmentation", "exportData": "You need at least 'viewer' permission to export data", "uploadImage": "You need 'edit' or 'owner' permission to upload images", "createProject": "You need to be logged in to create projects", "editProject": "You need 'owner' permission to edit project settings", "deleteProject": "You need 'owner' permission to delete projects", "inviteUsers": "You need 'owner' permission to invite users to the project", "removeUsers": "You need 'owner' permission to remove users from the project", "changePermissions": "You need 'owner' permission to change user permissions", "generic": "You don't have permission to perform this action"}}, "about": {"title": "About", "mission": {"title": "Our Mission", "description": "We are dedicated to advancing cell biology research through cutting-edge AI technology.", "vision": "Our vision is to make cell analysis more accessible and accurate for researchers worldwide."}, "technology": {"title": "Our Technology", "description": "We leverage state-of-the-art deep learning models for precise cell segmentation.", "feature1": {"title": "AI-Powered", "description": "Advanced neural networks for accurate cell detection"}, "feature2": {"title": "User-Friendly", "description": "Intuitive interface designed for researchers"}, "feature3": {"title": "Scalable", "description": "Process thousands of images efficiently"}}, "team": {"title": "Our Team", "description": "We are a diverse team of scientists and engineers passionate about advancing research.", "member1": {"name": "Team Member 1", "role": "Lead Developer"}, "member2": {"name": "Team Member 2", "role": "Research Scientist"}, "member3": {"name": "Team Member 3", "role": "Machine Learning Engineer"}}, "contact": {"title": "Get in Touch", "description": "We'd love to hear from you. Reach out with questions or feedback.", "email": "<EMAIL>"}}}