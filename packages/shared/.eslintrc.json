{
  "root": true,
  "env": {
    "browser": true,
    "es2020": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "ignorePatterns": ["dist", ".eslintrc.json", "node_modules", "coverage", "playwright-report", "test-results", "e2e/**/*", "eslint-rules/**/*", "*.config.ts", "*.config.js", "public/**/*", "scripts/**/*", "test-cache-reporter.ts", "vite-static-fix.js", "vite.config.enhanced.ts"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.json",
    "tsconfigRootDir": "."
  },
  "plugins": ["react-refresh", "unused-imports"],
  "rules": {
    // React specific rules
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "react/prop-types": "off",
    "react/display-name": "off",
    
    // TypeScript specific rules
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }
    ],
    
    // Unused imports
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "warn",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ],
    
    // General rules
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "prefer-const": "error",
    "no-var": "error",
    "no-empty": ["error", { "allowEmptyCatch": true }]
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "overrides": [
    {
      "files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
      "env": {
        "jest": true
      },
      "parserOptions": {
        "project": null
      },
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["vite.config.ts", "vitest.config.ts", "playwright.config.ts"],
      "parserOptions": {
        "project": null
      },
      "rules": {}
    },
    {
      "files": ["**/__mocks__/**/*", "**/scripts/**/*", "**/performance/**/*", "**/serviceWorkerRegistration.ts", "**/*visual-regression*/**/*", "**/*setupVisualRegression*"],
      "rules": {
        "no-console": "off",
        "@typescript-eslint/no-explicit-any": "warn",
        "react-hooks/rules-of-hooks": "off",
        "@typescript-eslint/no-namespace": "off"
      }
    }
  ]
}