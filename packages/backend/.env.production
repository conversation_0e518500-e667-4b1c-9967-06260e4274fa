# Production Environment Configuration
# IMPORTANT: Replace these values with secure production values

# Database Configuration
DATABASE_URL=**************************************/spheroseg
DB_POOL_MAX=100
DB_POOL_MIN=10
DB_IDLE_TIMEOUT_MS=30000
DB_CONNECTION_TIMEOUT_MS=2000
DB_ALLOW_EXIT_ON_IDLE=false

# Memory Configuration
CONTAINER_MEMORY_LIMIT_MB=2048
V8_MAX_OLD_SPACE_MB=1536
GC_INTERVAL_MS=60000
ENABLE_MANUAL_GC=true
NODE_OPTIONS="--max-old-space-size=1536 --expose-gc"

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_LEVEL=info
HEALTH_CHECK_DB_TIMEOUT=5000
HEALTH_CHECK_SLOW_RESPONSE=3000

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_MAX_RETRIES=3
REDIS_ENABLE_OFFLINE_QUEUE=true

# Security - MUST BE CHANGED FOR PRODUCTION
JWT_SECRET=${JWT_SECRET}  # Use environment variable
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
SESSION_SECRET=${SESSION_SECRET}  # Use environment variable
COOKIE_SECURE=true
COOKIE_HTTPONLY=true
COOKIE_SAMESITE=strict

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_LOGIN_MAX=5
RATE_LIMIT_SIGNUP_MAX=3

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_CREDENTIALS=true

# File Upload Limits
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/tiff,image/bmp
FILE_UPLOAD_RATE_LIMIT=10  # Per hour

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_DIR=/var/log/spheroseg

# Error Tracking (Optional - Sentry)
SENTRY_DSN=${SENTRY_DSN}
SENTRY_ENVIRONMENT=production
SENTRY_SAMPLE_RATE=0.1

# ML Service
ML_SERVICE_URL=http://ml:5002
ML_MAX_RETRIES=3
ML_RETRY_DELAY=5000
ML_TIMEOUT=300000  # 5 minutes

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # 2 AM daily
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}
BACKUP_S3_REGION=${BACKUP_S3_REGION}

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_ENABLED=true
GRAFANA_PORT=3000

# Feature Flags
ENABLE_WEBSOCKET=true
ENABLE_QUEUE_MONITORING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_USER_ANALYTICS=false  # GDPR compliance