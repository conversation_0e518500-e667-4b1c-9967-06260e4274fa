# Backend Environment Variables Example
# Copy this file to .env and update with your values

# Node Environment
NODE_ENV=development

# Server Configuration
PORT=5001
HOST=0.0.0.0
APP_URL=http://localhost:5001
ALLOWED_ORIGINS=http://localhost:3000,http://localhost

# Database Configuration
DB_HOST=db
DB_PORT=5432
DB_NAME=spheroseg
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false
DB_MAX_CONNECTIONS=10

# Authentication
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=1d
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d
BCRYPT_SALT_ROUNDS=10
SESSION_TIMEOUT=3600
TOKEN_SECURITY_MODE=standard

# Logging Configuration
# Log Levels: error, warn, info, http, verbose, debug, silly
# Production default: info, Development default: debug
LOG_LEVEL=info
LOG_TO_FILE=false
LOG_DIR=./logs

# ML Service
ML_SERVICE_URL=http://ml:5002
ML_MAX_RETRIES=3
ML_RETRY_DELAY=5000
ML_MAX_CONCURRENT_TASKS=2
ML_HEALTH_CHECK_INTERVAL=60000
ML_QUEUE_UPDATE_INTERVAL=5000

# Storage
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=50000000
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/tiff,image/tif,image/bmp
DEFAULT_USER_STORAGE_LIMIT=10737418240
MAX_TOTAL_STORAGE_BYTES=107374182400
STORAGE_WARNING_THRESHOLD=0.8
TEMP_FILE_MAX_AGE_HOURS=24
CLEANUP_SCHEDULE_HOURS=6
ENABLE_ORPHANED_FILE_CLEANUP=true
THUMBNAIL_QUALITY=80
THUMBNAIL_MAX_WIDTH=300
THUMBNAIL_MAX_HEIGHT=300

# Monitoring
METRICS_ENABLED=true
REQUEST_TIMEOUT_MS=30000
METRICS_PREFIX=spheroseg_

# Security
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
ENABLE_HELMET=true
ENABLE_RATE_LIMIT=true
CSRF_ENABLED=true
USE_REDIS_RATE_LIMIT=false
IP_WHITELIST=

# Redis (optional, for rate limiting)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Email (optional)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=
EMAIL_PASS=

# RabbitMQ (optional)
RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
RABBITMQ_QUEUE=segmentation_tasks

# CDN Configuration
CDN_ENABLED=false
CDN_PROVIDER=none
CDN_BASE_URL=
CDN_ASSET_PREFIX=/assets
CDN_IMAGE_PREFIX=/uploads

# CDN Cache Control
CDN_CACHE_IMAGES=public, max-age=********, immutable
CDN_CACHE_CSS=public, max-age=********, immutable
CDN_CACHE_JS=public, max-age=********, immutable
CDN_CACHE_FONTS=public, max-age=********, immutable
CDN_CACHE_DEFAULT=public, max-age=3600

# CDN Security
CDN_SIGNED_URLS=false
CDN_SIGNED_URL_EXPIRY=3600
CDN_SECRET_KEY=

# CloudFront Configuration
CDN_CF_DISTRIBUTION_ID=
CDN_CF_KEYPAIR_ID=
CDN_CF_PRIVATE_KEY=
CDN_S3_BUCKET=

# Cloudflare Configuration
CDN_CLOUDFLARE_ZONE_ID=
CDN_CLOUDFLARE_API_TOKEN=
CDN_CLOUDFLARE_ACCOUNT_ID=

# CDN Invalidation
CDN_INVALIDATION_ENABLED=false
CDN_INVALIDATION_PATTERNS=
CDN_INVALIDATION_MAX_RETRIES=3