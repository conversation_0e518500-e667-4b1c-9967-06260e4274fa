{"name": "@spheroseg/backend", "version": "1.0.0", "license": "MIT", "main": "dist/server.js", "scripts": {"build": "tsc && tsc-alias", "start": "node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --no-notify --no-deps --ignore-watch node_modules src/server.ts", "test": "jest", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathIgnorePatterns=integration", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "migrate:access-requests": "node src/scripts/run-migration.js", "audit": "node scripts/dependency-audit.js", "audit:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "node scripts/update-dependencies.js", "rotate-secrets": "ts-node -r tsconfig-paths/register ../../scripts/rotate-secrets.ts"}, "dependencies": {"@spheroseg/shared": "file:../shared", "amqplib": "^0.10.3", "archiver": "^5.3.2", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "commander": "^12.0.0", "compression": "^1.8.0", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.17.1", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-validator": "^7.2.0", "helmet": "^7.2.0", "i18next": "^25.3.2", "i18next-fs-backend": "^2.6.0", "i18next-http-middleware": "^3.7.4", "ioredis": "^5.4.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwks-rsa": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "pg": "^8.15.6", "polygon-clipping": "^0.15.7", "prom-client": "^15.1.2", "rate-limiter-flexible": "^5.0.4", "sharp": "^0.34.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.3"}, "devDependencies": {"@spheroseg/types": "file:../types", "@types/amqplib": "^0.10.7", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/connect-redis": "^0.0.23", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/express-rate-limit": "^5.1.3", "@types/express-session": "^1.18.0", "@types/helmet": "^0.0.48", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^18.19.117", "@types/node-cache": "^4.2.5", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/opossum": "^8.1.9", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "exceljs": "^4.4.0", "jest": "^29.7.0", "node-fetch": "^3.3.2", "opossum": "^9.0.0", "pg-mem": "^3.0.5", "prettier": "^3.2.5", "rimraf": "^5.0.5", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0"}, "keywords": [], "author": "", "types": "./dist/server.d.ts", "description": ""}