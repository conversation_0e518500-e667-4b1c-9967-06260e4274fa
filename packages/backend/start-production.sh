#!/bin/bash

# Production start script for SpherosegV4 backend

echo "🚀 Starting SpherosegV4 backend in production mode..."

# Load environment variables
if [ -f .env.production ]; then
    echo "📄 Loading production environment variables..."
    export $(cat .env.production | grep -v '^#' | xargs)
else
    echo "⚠️  Warning: .env.production file not found"
fi

# Set production environment
export NODE_ENV=production

# Ensure we're in the backend directory
cd /home/<USER>/spheroseg/packages/backend

# Check if build exists
if [ ! -d "dist" ]; then
    echo "❌ Error: dist directory not found. Run ./deploy-production-workaround.sh first"
    exit 1
fi

# Start the server
echo "🏃 Starting server..."
echo "🌍 Environment: $NODE_ENV"
echo "🔧 Database: $DATABASE_URL"
echo "🚪 Port: ${PORT:-5001}"
echo ""

# Start with PM2 if available, otherwise use node
if command -v pm2 &> /dev/null; then
    echo "📊 Starting with PM2..."
    pm2 start dist/backend/src/server.js --name "spheroseg-backend" --env production
    pm2 logs spheroseg-backend
else
    echo "📊 Starting with Node.js..."
    node dist/backend/src/server.js
fi