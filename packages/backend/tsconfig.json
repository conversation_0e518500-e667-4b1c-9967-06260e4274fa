{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020"], "types": ["node", "jest"], "outDir": "dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@spheroseg/types": ["../types/src"], "@spheroseg/shared": ["../shared/src"]}, "moduleResolution": "node", "noEmit": false, "allowImportingTsExtensions": false, "isolatedModules": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true, "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**", "**/testing/**", "**/examples/**", "**/test-utils/**"]}