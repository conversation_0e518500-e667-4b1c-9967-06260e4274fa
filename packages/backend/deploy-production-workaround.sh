#!/bin/bash

# Production deployment workaround script
# This script compiles the backend with TypeScript errors ignored
# This is a temporary solution until all TypeScript errors are fixed

echo "🚀 Starting production deployment (with TypeScript workaround)..."

# Ensure we're in the backend directory
cd /home/<USER>/spheroseg/packages/backend

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Build with TypeScript errors ignored
echo "🔨 Building backend (ignoring TypeScript errors)..."
npx tsc --noEmitOnError false || true
npx tsc-alias || true

# Check if dist directory was created
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not created"
    exit 1
fi

# Check if main server file exists
if [ ! -f "dist/backend/src/server.js" ]; then
    echo "❌ Build failed - server.js not found"
    exit 1
fi

echo "✅ Build completed successfully (with TypeScript errors ignored)"
echo "📦 Production build ready in dist/"
echo ""
echo "⚠️  WARNING: This build includes TypeScript errors that should be fixed"
echo "⚠️  This is a temporary workaround for production deployment"
echo ""
echo "To start the production server:"
echo "  cd /home/<USER>/spheroseg/packages/backend"
echo "  NODE_ENV=production node dist/backend/src/server.js"