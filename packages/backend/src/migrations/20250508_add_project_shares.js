const fs = require('fs');
const path = require('path');

/**
 * @param {import('pg').Pool} db - PostgreSQL connection pool
 */
async function up(db) {
  const sqlPath = path.join(__dirname, 'add-project-shares-table.sql');
  const sql = fs.readFileSync(sqlPath, 'utf8');
  
  
  await db.query(sql);
  
}

/**
 * @param {import('pg').Pool} db - PostgreSQL connection pool
 */
async function down(db) {
  
  
  // Drop the view first
  await db.query('DROP VIEW IF EXISTS user_shared_projects');
  
  // Drop the table with cascade to remove dependent objects
  await db.query('DROP TABLE IF EXISTS project_shares CASCADE');
  
  
}

module.exports = {
  up,
  down
};