{"auth": {"loginFailed": "Přihlášení se<PERSON>", "invalidCredentials": "Neplatný e-mail nebo heslo", "userNotFound": "<PERSON><PERSON><PERSON><PERSON>", "emailAlreadyExists": "E-mail již <PERSON>uje", "registrationFailed": "Registrace už<PERSON>le selhala", "invalidToken": "Neplatný token", "tokenExpired": "Token vypršel", "refreshTokenInvalid": "Neplatný obnovovací token", "authRequired": "Vyžaduje se ověření", "insufficientPermissions": "Nedostatečná oprávnění", "accountLocked": "<PERSON><PERSON><PERSON> byl uzamčen. Prosím kontaktujte podporu.", "loginSuccess": "Přihlášení úspěšné"}, "validation": {"required": "{{field}} je povinn<PERSON>", "invalidEmail": "Neplatný formát e-mailu", "passwordTooShort": "Heslo musí mít alespoň 8 znaků", "invalidFormat": "Neplatný formát {{field}}", "alreadyExists": "{{field}} již <PERSON>uje", "notFound": "{{field}} ne<PERSON><PERSON>", "invalidImageId": "Neplatný formát ID obrázku", "atLeastOneRequired": "Alespoň jeden {{field}} je vy<PERSON><PERSON>"}, "project": {"notFound": "Projekt nenalezen", "notAuthorized": "Nemáte oprávnění {{action}} tento projekt", "createdSuccess": "Projekt byl úspěšně vytvořen", "updatedSuccess": "Projekt byl úspěšně aktualizován", "deletedSuccess": "Projekt byl úspěšně s<PERSON>", "duplicateSuccess": "Projekt byl úspěšně zdu<PERSON>lik<PERSON>n", "shareSuccess": "Projekt byl úspěšně sdílen", "shareRemoved": "Sdílení bylo <PERSON> odebráno", "invitationSent": "Pozvánka byla úspěšně odeslána", "invitationAccepted": "Pozvánka byla úspěšně přijata", "invitationInvalid": "Neplatná nebo vypršelá pozvánka"}, "image": {"uploadSuccess": "Obrázek byl úspěšně nahrán", "uploadFailed": "Nahrání obrázku selhalo", "notFound": "Obrá<PERSON>k <PERSON>", "deletedSuccess": "Obrázek byl úspěšně s<PERSON>", "segmentationQueued": "Segmentace byla úspěšně zařazena do fronty", "segmentationFailed": "Segmentace selhala", "resegmentationQueued": "Resegmentace byla <PERSON>šně zařazena do fronty"}, "email": {"invitationSubject": "<PERSON><PERSON> j<PERSON> p<PERSON>váni ke spolup<PERSON>áci na \"{{projectName}}\"", "invitationAcceptedSubject": "{{userName}} p<PERSON><PERSON><PERSON> vaši pozvánku k projektu", "invitationBody": "{{ownerName}} vás pozval ke spolupráci na projektu \"{{projectName}}\" na SpheroSeg.", "invitationAction": "Přijmout p<PERSON>vánku", "invitationExpires": "Tento odkaz pozvánky vyprší za 7 dní."}, "error": {"internalServer": "Interní ch<PERSON>", "somethingWentWrong": "Něco se pokazilo", "resourceNotFound": "<PERSON><PERSON><PERSON><PERSON>", "resourceConflict": "Konflikt zdrojů", "fileTooLarge": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON><PERSON>", "unexpectedField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> souboru", "validationFailed": "<PERSON><PERSON><PERSON> se<PERSON>"}, "success": {"created": "{{resource}} byl <PERSON>", "updated": "{{resource}} byl ú<PERSON>šně aktualizován", "deleted": "{{resource}} by<PERSON><PERSON>", "fetched": "{{resource}} byl <PERSON><PERSON>"}, "common": {"welcome": "Vítejte v SpheroSeg API", "healthCheck": "API je v pořádku", "user": "<PERSON>živatel", "project": "Projekt", "image": "Obrázek", "segmentation": "Segmentace"}}