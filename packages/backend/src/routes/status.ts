import express, { Request, Response, Router, NextFunction } from 'express';
import { authenticate as authMiddleware, AuthenticatedRequest } from '../security/middleware/auth';
import { getSegmentationQueueStatus } from '../services/segmentationService';
import { getPool } from '../db';
import logger from '../utils/logger';

const router: Router = express.Router();

// GET / - Simple health check endpoint
router.get('/', (_req: Request, res: Response) => {
  logger.debug('Health check endpoint called');
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env["npm_package_version"] || '1.0.0',
  });
});

// GET /check - Dedicated health check endpoint for Docker
router.get('/check', (_req: Request, res: Response) => {
  logger.debug('Docker health check endpoint called');
  res.status(200).send('OK');
});

// GET /api/queue-status - Get the current status of the segmentation queue with image details
router.get(
  '/queue-status',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Get basic queue status from segmentation service
      const queueStatus = await getSegmentationQueueStatus();
      const { queueLength, runningTasks } = queueStatus;

      // Get image details for running tasks
      const processingImages: {
        id: string;
        name: string;
        projectId: string;
      }[] = [];

      if (runningTasks.length > 0) {
        // Query database to get image names for the running tasks
        const imagesQuery = await getPool().query(
          `SELECT i.id, i.name, i.project_id
         FROM images i
         JOIN projects p ON i.project_id = p.id
         WHERE i.id = ANY($1::uuid[]) AND p.user_id = $2`,
          [runningTasks, userId]
        );

        // Map the results to the expected format
        for (const image of imagesQuery.rows) {
          processingImages.push({
            id: image.id,
            name: image.name,
            projectId: image.project_id,
          });
        }
      }

      // Return enhanced queue status
      res.status(200).json({
        queueLength,
        runningTasks,
        processingImages,
      });
    } catch (error) {
      
      next(error);
    }
  }
);

// GET /api/queue-status/:projectId - Get queue status filtered by project
router.get(
  '/queue-status/:projectId',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, _next: NextFunction) => {
    const userId = req.user?.userId;
    const projectId = req.params["projectId"];

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Use projectService to check access (ownership or sharing)
      const projectService = await import('../services/projectService');
      const project = await projectService.getProjectById(getPool() as any, projectId, userId);

      if (!project) {
        res.status(404).json({ message: 'Project not found or access denied' });
        return;
      }

      // Get basic queue status from segmentation service
      const queueStatus = await getSegmentationQueueStatus();
      const { runningTasks, pendingTasks } = queueStatus;

      // Get image details for running tasks, filtered by project
      const imagesQuery = await getPool().query(
        `SELECT i.id, i.name
       FROM images i
       WHERE i.id = ANY($1::uuid[]) AND i.project_id = $2`,
        [runningTasks, projectId]
      );

      // Map the results to the expected format
      const processingImages = imagesQuery.rows.map((image) => ({
        id: image.id,
        name: image.name,
        projectId: projectId,
      }));

      // Get queued images for this project
      // Since we don't have project info in the queue, we need to query the database
      let projectQueuedTasks: string[] = [];
      if (pendingTasks && pendingTasks.length > 0) {
        const queuedImagesQuery = await getPool().query(
          `SELECT i.id
         FROM images i
         WHERE i.id = ANY($1::uuid[]) AND i.project_id = $2`,
          [pendingTasks, projectId]
        );

        projectQueuedTasks = queuedImagesQuery.rows.map((row) => row.id);
      }

      // Return project-specific queue status
      res.status(200).json({
        queueLength: projectQueuedTasks.length,
        runningTasks: processingImages.map((img) => img.id),
        queuedTasks: projectQueuedTasks,
        pendingTasks: projectQueuedTasks, // Include both for compatibility
        processingImages,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      
      // Return empty queue status instead of error to prevent frontend from crashing
      res.status(200).json({
        queueLength: 0,
        runningTasks: [],
        queuedTasks: [],
        pendingTasks: [],
        processingImages: [],
        timestamp: new Date().toISOString(),
      });
    }
  }
);

// GET /api/mock-queue-status - Get mock queue status for development
router.get(
  '/mock-queue-status',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Return mock data to demonstrate the UI during development
      // Generate some random UUIDs for demonstration
      const mockImageId1 = '123e4567-e89b-12d3-a456-************';
      const mockImageId2 = '223e4567-e89b-12d3-a456-************';
      const mockQueuedId1 = '323e4567-e89b-12d3-a456-************';
      const mockQueuedId2 = '423e4567-e89b-12d3-a456-************';

      // Return mock data
      res.json({
        queueLength: 2,
        runningTasks: [mockImageId1, mockImageId2],
        queuedTasks: [mockQueuedId1, mockQueuedId2],
        processingImages: [
          {
            id: mockImageId1,
            name: 'Sample Image 1',
            projectId: req.query["projectId"] || 'project-123',
          },
          {
            id: mockImageId2,
            name: 'Sample Image 2',
            projectId: 'project-456',
          },
        ],
      });
    } catch (error) {
      
      next(error);
    }
  }
);

// GET /api/mock-queue-status/:projectId - Get mock queue status for a specific project
router.get(
  '/mock-queue-status/:projectId',
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const userId = req.user?.userId;
    const projectId = req.params["projectId"];

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Return mock data specific to the requested project
      const mockImageId1 = '123e4567-e89b-12d3-a456-************';
      const mockQueuedId1 = '323e4567-e89b-12d3-a456-************';

      // Return mock data
      res.json({
        queueLength: 1,
        runningTasks: [mockImageId1],
        queuedTasks: [mockQueuedId1],
        processingImages: [
          {
            id: mockImageId1,
            name: `Sample Image for Project ${projectId}`,
            projectId: projectId,
          },
        ],
      });
    } catch (error) {
      
      next(error);
    }
  }
);

export default router;
