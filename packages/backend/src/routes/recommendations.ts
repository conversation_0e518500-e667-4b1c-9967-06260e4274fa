/**
 * Unified Recommendations API Routes
 * 
 * Provides comprehensive recommendation endpoints that aggregate insights
 * from all system components including performance, database, ML, and security.
 */

import { Router, Request, Response } from 'express';
import { requireAdmin } from '../security/middleware/auth';
import logger from '../utils/logger';
import recommendationAggregator, { RecommendationContext } from '../services/recommendationAggregator';
import { getPool } from '../db';
import { RecommendationValidator } from '../utils/recommendationValidator';

const router = Router();

/**
 * GET /api/recommendations
 * Get all aggregated recommendations
 */
router.get('/', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { source, minPriority, type, impact } = req.query;
    
    // Build context from current system state
    const context = await buildRecommendationContext();
    
    // Get all recommendations
    let recommendations = await recommendationAggregator.aggregateRecommendations(context);
    
    // Apply filters
    if (source && typeof source === 'string') {
      recommendations = recommendations.filter(rec => rec.source === source);
    }
    
    if (minPriority && typeof minPriority === 'string') {
      const priority = parseInt(minPriority, 10);
      if (!isNaN(priority)) {
        recommendations = recommendations.filter(rec => rec.priority >= priority);
      }
    }
    
    if (type && typeof type === 'string') {
      recommendations = recommendations.filter(rec => rec.type === type);
    }
    
    if (impact && typeof impact === 'string') {
      recommendations = recommendations.filter(rec => rec.impact === impact);
    }
    
    // Get statistics
    const stats = recommendationAggregator.getStatistics();
    
    // Validate recommendations
    const validation = RecommendationValidator.validateRecommendations(recommendations);
    const priorityDistribution = RecommendationValidator.validatePriorityDistribution(recommendations);
    
    res.json({
      success: true,
      recommendations,
      total: recommendations.length,
      statistics: stats,
      context,
      validation: {
        isValid: validation.isValid,
        warnings: validation.warnings,
        priorityDistribution: priorityDistribution.distribution,
        prioritySuggestions: priorityDistribution.suggestions,
      },
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    logger.error('Error fetching unified recommendations', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch recommendations',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/recommendations/critical
 * Get only critical recommendations (priority >= 90)
 */
router.get('/critical', requireAdmin, async (req: Request, res: Response) => {
  try {
    const context = await buildRecommendationContext();
    await recommendationAggregator.aggregateRecommendations(context);
    
    const criticalRecommendations = recommendationAggregator.getHighPriorityRecommendations(90);
    
    res.json({
      success: true,
      recommendations: criticalRecommendations,
      total: criticalRecommendations.length,
      context,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    logger.error('Error fetching critical recommendations', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch critical recommendations',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/recommendations/by-source/:source
 * Get recommendations from a specific source
 */
router.get('/by-source/:source', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { source } = req.params;
    
    const context = await buildRecommendationContext();
    await recommendationAggregator.aggregateRecommendations(context);
    
    const sourceRecommendations = recommendationAggregator.getRecommendationsBySource(source);
    
    res.json({
      success: true,
      source,
      recommendations: sourceRecommendations,
      total: sourceRecommendations.length,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    logger.error('Error fetching recommendations by source', { error, source: req.params.source });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch recommendations by source',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/recommendations/:id/implement
 * Mark a recommendation as implemented
 */
router.post('/:id/implement', requireAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    
    const success = recommendationAggregator.markAsImplemented(id);
    
    if (success) {
      logger.info('Recommendation marked as implemented', {
        recommendationId: id,
        implementedBy: (req as any).user?.userId,
        notes,
      });
      
      res.json({
        success: true,
        message: 'Recommendation marked as implemented',
        recommendationId: id,
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Recommendation not found',
      });
    }
    
  } catch (error) {
    logger.error('Error marking recommendation as implemented', { error, id: req.params.id });
    res.status(500).json({
      success: false,
      error: 'Failed to mark recommendation as implemented',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/recommendations/summary
 * Get a summary of all recommendations
 */
router.get('/summary', requireAdmin, async (req: Request, res: Response) => {
  try {
    const context = await buildRecommendationContext();
    const recommendations = await recommendationAggregator.aggregateRecommendations(context);
    const stats = recommendationAggregator.getStatistics();
    
    // Group by category
    const byCategory: Record<string, number> = {};
    recommendations.forEach(rec => {
      byCategory[rec.category] = (byCategory[rec.category] || 0) + 1;
    });
    
    // Calculate estimated effort
    let totalLowEffort = 0, totalMediumEffort = 0, totalHighEffort = 0;
    recommendations.forEach(rec => {
      if (rec.effort === 'low') totalLowEffort++;
      else if (rec.effort === 'medium') totalMediumEffort++;
      else if (rec.effort === 'high') totalHighEffort++;
    });
    
    const summary = {
      totalRecommendations: stats.total,
      byPriority: stats.byPriority,
      bySource: stats.bySource,
      byCategory,
      byEffort: {
        low: totalLowEffort,
        medium: totalMediumEffort,
        high: totalHighEffort,
      },
      implemented: stats.implemented,
      implementationRate: stats.total > 0 ? (stats.implemented / stats.total) * 100 : 0,
      topRecommendations: recommendations.slice(0, 5).map(rec => ({
        id: rec.id,
        title: rec.title,
        priority: rec.priority,
        impact: rec.impact,
        effort: rec.effort,
      })),
      systemContext: context,
    };
    
    res.json({
      success: true,
      summary,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    logger.error('Error generating recommendations summary', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to generate recommendations summary',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * Build recommendation context from current system state
 */
async function buildRecommendationContext(): Promise<RecommendationContext> {
  try {
    const pool = getPool();
    
    // Get queue length
    const queueResult = await pool.query(
      'SELECT COUNT(*) as count FROM segmentation_queue WHERE status IN ($1, $2)',
      ['queued', 'processing']
    );
    const queueLength = parseInt(queueResult.rows[0]?.count || '0', 10);
    
    // Get active user count (last 5 minutes)
    let userCount = 0;
    try {
      // Check if user_activity table exists
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'user_activity'
        )
      `);
      
      if (tableCheck.rows[0].exists) {
        const userResult = await pool.query(
          'SELECT COUNT(DISTINCT user_id) as count FROM user_activity WHERE last_activity > NOW() - INTERVAL \'5 minutes\'',
        );
        userCount = parseInt(userResult.rows[0]?.count || '0', 10);
      }
    } catch (err) {
      logger.debug('User activity table not available', { error: err });
      // Continue with default value
    }
    
    // Get error rate from last hour
    // Note: This is a placeholder - would need actual error tracking table
    const errorRate = 2.5; // Placeholder
    
    // Get request rate (placeholder)
    const requestRate = 150; // requests per minute placeholder
    
    // Get system metrics
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      systemLoad: 0.65, // Placeholder - would calculate from actual metrics
      errorRate,
      memoryUsage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to percentage
      queueLength,
      userCount,
      requestRate,
    };
    
  } catch (error) {
    logger.error('Error building recommendation context', { error });
    
    // Return default context on error
    return {
      systemLoad: 0,
      errorRate: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      queueLength: 0,
      userCount: 0,
      requestRate: 0,
    };
  }
}

export default router;