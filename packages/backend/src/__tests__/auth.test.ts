/**
 * Authentication Tests
 *
 * This file contains tests for the authentication endpoints and middleware.
 */

import request from 'supertest';
import app from '../app';
import jwt from 'jsonwebtoken';
import config from '../config';
import db from '../db';
import {
  createMockQueryResult,
  MockedQuery,
  MockedBcryptCompare,
  MockedBcryptHash,
} from './types/mocks';

// Mock database queries
jest.mock('../db', () => {
  const mockQuery = jest.fn();
  return {
    __esModule: true,
    default: {
      query: mockQuery,
    },
    query: mockQuery,
  };
});

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
}));

// Get the mocked functions
import bcryptjs from 'bcryptjs';
const mockQuery = (db as jest.Mocked<typeof db>).query as MockedQuery;
const mockCompare = bcryptjs.compare as MockedBcryptCompare;
const mockHash = bcryptjs.hash as MockedBcryptHash;

describe('Authentication Endpoints', () => {
  // Clear mock calls before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/login', () => {
    it('should return 400 if email is missing', async () => {
      const response = await request(app).post('/api/auth/login').send({ password: 'password123' });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 if password is missing', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>' });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 401 if user not found', async () => {
      // Mock query to return empty result
      mockQuery.mockResolvedValueOnce(createMockQueryResult([]));

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message');
      expect(mockQuery).toHaveBeenCalledTimes(1);
    });

    it('should return 401 if password is incorrect', async () => {
      // Mock user found but password check fails
      mockQuery.mockResolvedValueOnce(
        createMockQueryResult([
          {
            id: '123',
            email: '<EMAIL>',
            password_hash: 'hashedPasswordInvalid',
          },
        ])
      );

      // Mock bcrypt.compare to return false (password doesn't match)
      mockCompare.mockResolvedValueOnce(false);

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'wrongpassword' });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message');
    });

    it('should return 200 with token if login successful', async () => {
      // Mock user found
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        password_hash: 'hashedPassword',
      };

      // Mock successful user query
      mockQuery.mockResolvedValueOnce(createMockQueryResult([mockUser]));

      // Mock bcrypt.compare to return true (password matches)
      mockCompare.mockResolvedValueOnce(true);

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('refreshToken');
      expect(mockQuery).toHaveBeenCalled();
      expect(mockCompare).toHaveBeenCalledWith('password123', 'hashedPassword');
    });
  });

  describe('POST /api/auth/register', () => {
    it('should return 400 if required fields are missing', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({ email: '<EMAIL>' }); // Missing password

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 409 if email already exists', async () => {
      // Mock query to check if email exists - returns user found
      mockQuery.mockResolvedValueOnce(createMockQueryResult([{ email: '<EMAIL>' }]));

      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('message');
      expect(mockQuery).toHaveBeenCalledTimes(1);
    });

    it('should return 201 if registration successful', async () => {
      // Mock queries for registration
      mockQuery
        // First query: check if email exists (return empty result)
        .mockResolvedValueOnce(createMockQueryResult([]))
        // Second query: start transaction
        .mockResolvedValueOnce(createMockQueryResult([]))
        // Third query: insert user (return user ID)
        .mockResolvedValueOnce(createMockQueryResult([{ id: '123' }]))
        // Fourth query: insert profile
        .mockResolvedValueOnce(createMockQueryResult([{ user_id: '123' }]))
        // Fifth query: commit transaction
        .mockResolvedValueOnce(createMockQueryResult([]));

      // Mock bcrypt.hash for password
      mockHash.mockResolvedValueOnce('hashedPassword');

      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
      });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('refreshToken');
      expect(mockHash).toHaveBeenCalledWith('password123', expect.any(Number));
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return 401 if no token provided', async () => {
      const response = await request(app).get('/api/auth/me');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 401 if token is invalid', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return user data if token is valid', async () => {
      // Create a valid token
      const user = { userId: '123', email: '<EMAIL>' };
      const token = jwt.sign(user, config.auth.jwtSecret, { expiresIn: '1h' });

      // Mock user query
      mockQuery.mockResolvedValueOnce(createMockQueryResult([
        {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      ]));

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', '123');
      expect(response.body).toHaveProperty('email', '<EMAIL>');
      expect(mockQuery).toHaveBeenCalledTimes(1);
    });
  });
});

describe('Authentication Middleware', () => {
  it('should protect routes that require authentication', async () => {
    const response = await request(app).get('/api/projects'); // Protected route

    expect(response.status).toBe(401);
    expect(response.body).toHaveProperty('error');
  });

  it('should allow access to protected routes with valid token', async () => {
    // Create a valid token
    const user = { userId: '123', email: '<EMAIL>' };
    const token = jwt.sign(user, config.auth.jwtSecret, { expiresIn: '1h' });

    // Mock project query
    mockQuery.mockResolvedValueOnce(createMockQueryResult([]));

    const response = await request(app)
      .get('/api/projects')
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
  });
});
