-- Creating the refresh_tokens table for JWT refresh token storage and management

CREATE TABLE IF NOT EXISTS refresh_tokens (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token_id VARCHAR(255) NOT NULL UNIQUE,
  is_revoked BOOLEAN NOT NULL DEFAULT false,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS refresh_tokens_user_id_idx ON refresh_tokens(user_id);
CREATE INDEX IF NOT EXISTS refresh_tokens_token_id_idx ON refresh_tokens(token_id);
CREATE INDEX IF NOT EXISTS refresh_tokens_expires_at_idx ON refresh_tokens(expires_at);

-- Set up automatic cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_refresh_tokens()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  DELETE FROM refresh_tokens 
  WHERE expires_at < NOW() - INTERVAL '1 day';
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if exists
DROP TRIGGER IF EXISTS trigger_cleanup_expired_refresh_tokens ON refresh_tokens;

-- Create trigger to run cleanup function periodically
CREATE TRIGGER trigger_cleanup_expired_refresh_tokens
AFTER INSERT ON refresh_tokens
EXECUTE PROCEDURE cleanup_expired_refresh_tokens();