/**
 * Recommendation Validator Utility
 * 
 * Validates recommendation data structures for consistency and completeness
 */

import { UnifiedRecommendation } from '../services/recommendationAggregator';
import logger from './logger';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations?: UnifiedRecommendation[];
}

export class RecommendationValidator {
  
  /**
   * Validate a single recommendation
   */
  public static validateRecommendation(recommendation: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Required fields
    if (!recommendation.id) {
      errors.push('Recommendation ID is required');
    }
    
    if (!recommendation.source) {
      errors.push('Source is required');
    } else if (!this.isValidSource(recommendation.source)) {
      errors.push(`Invalid source: ${recommendation.source}`);
    }
    
    if (!recommendation.type) {
      errors.push('Type is required');
    } else if (!this.isValidType(recommendation.type)) {
      errors.push(`Invalid type: ${recommendation.type}`);
    }
    
    if (recommendation.priority === undefined || recommendation.priority === null) {
      errors.push('Priority is required');
    } else if (typeof recommendation.priority !== 'number') {
      errors.push('Priority must be a number');
    } else if (recommendation.priority < 0 || recommendation.priority > 100) {
      errors.push('Priority must be between 0 and 100');
    }
    
    if (!recommendation.title) {
      errors.push('Title is required');
    } else if (recommendation.title.length > 200) {
      warnings.push('Title is very long (>200 characters)');
    }
    
    if (!recommendation.description) {
      errors.push('Description is required');
    }
    
    if (!recommendation.impact) {
      errors.push('Impact is required');
    } else if (!this.isValidImpact(recommendation.impact)) {
      errors.push(`Invalid impact: ${recommendation.impact}`);
    }
    
    if (!recommendation.effort) {
      errors.push('Effort is required');
    } else if (!this.isValidEffort(recommendation.effort)) {
      errors.push(`Invalid effort: ${recommendation.effort}`);
    }
    
    if (!recommendation.category) {
      errors.push('Category is required');
    }
    
    if (!recommendation.actionItems || !Array.isArray(recommendation.actionItems)) {
      errors.push('Action items must be an array');
    } else if (recommendation.actionItems.length === 0) {
      warnings.push('No action items provided');
    }
    
    if (!recommendation.timestamp) {
      errors.push('Timestamp is required');
    } else if (!(recommendation.timestamp instanceof Date)) {
      errors.push('Timestamp must be a Date object');
    }
    
    // Optional fields validation
    if (recommendation.estimatedTimeToImplement && 
        typeof recommendation.estimatedTimeToImplement !== 'string') {
      warnings.push('estimatedTimeToImplement should be a string');
    }
    
    if (recommendation.potentialSavings && 
        typeof recommendation.potentialSavings !== 'object') {
      warnings.push('potentialSavings should be an object');
    }
    
    if (recommendation.expires && !(recommendation.expires instanceof Date)) {
      errors.push('expires must be a Date object if provided');
    }
    
    if (recommendation.implemented !== undefined && 
        typeof recommendation.implemented !== 'boolean') {
      errors.push('implemented must be a boolean if provided');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
  
  /**
   * Validate multiple recommendations
   */
  public static validateRecommendations(recommendations: any[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const validRecommendations: UnifiedRecommendation[] = [];
    
    if (!Array.isArray(recommendations)) {
      errors.push('Recommendations must be an array');
      return { isValid: false, errors, warnings };
    }
    
    const seenIds = new Set<string>();
    
    recommendations.forEach((rec, index) => {
      const validation = this.validateRecommendation(rec);
      
      if (validation.errors.length > 0) {
        errors.push(`Recommendation at index ${index}: ${validation.errors.join(', ')}`);
      } else {
        // Check for duplicate IDs
        if (seenIds.has(rec.id)) {
          errors.push(`Duplicate recommendation ID: ${rec.id}`);
        } else {
          seenIds.add(rec.id);
          validRecommendations.push(rec as UnifiedRecommendation);
        }
      }
      
      if (validation.warnings.length > 0) {
        warnings.push(`Recommendation at index ${index}: ${validation.warnings.join(', ')}`);
      }
    });
    
    // Additional cross-recommendation validations
    const priorityGroups = this.groupByPriority(validRecommendations);
    
    if (priorityGroups.critical > 10) {
      warnings.push('Too many critical recommendations (>10). Consider re-evaluating priorities.');
    }
    
    if (priorityGroups.critical === 0 && priorityGroups.high === 0 && recommendations.length > 5) {
      warnings.push('No high-priority recommendations found. Consider if priorities are too conservative.');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      recommendations: validRecommendations,
    };
  }
  
  /**
   * Sanitize recommendation data
   */
  public static sanitizeRecommendation(recommendation: any): UnifiedRecommendation | null {
    try {
      // Ensure required fields have defaults
      const sanitized: UnifiedRecommendation = {
        id: recommendation.id || `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        source: this.isValidSource(recommendation.source) ? recommendation.source : 'performance',
        type: this.isValidType(recommendation.type) ? recommendation.type : 'optimization',
        priority: this.clampPriority(recommendation.priority),
        title: (recommendation.title || recommendation.description || 'Unknown Recommendation').substring(0, 200),
        description: recommendation.description || recommendation.title || 'No description available',
        impact: this.isValidImpact(recommendation.impact) ? recommendation.impact : 'medium',
        effort: this.isValidEffort(recommendation.effort) ? recommendation.effort : 'medium',
        category: recommendation.category || 'general',
        actionItems: Array.isArray(recommendation.actionItems) ? recommendation.actionItems : [recommendation.description],
        timestamp: recommendation.timestamp instanceof Date ? recommendation.timestamp : new Date(),
        estimatedTimeToImplement: recommendation.estimatedTimeToImplement,
        potentialSavings: recommendation.potentialSavings,
        metadata: recommendation.metadata,
        expires: recommendation.expires instanceof Date ? recommendation.expires : undefined,
        implemented: typeof recommendation.implemented === 'boolean' ? recommendation.implemented : false,
      };
      
      return sanitized;
    } catch (error) {
      logger.error('Failed to sanitize recommendation', { error, recommendation });
      return null;
    }
  }
  
  /**
   * Validate recommendation priority distribution
   */
  public static validatePriorityDistribution(recommendations: UnifiedRecommendation[]): {
    isBalanced: boolean;
    distribution: Record<string, number>;
    suggestions: string[];
  } {
    const distribution = this.groupByPriority(recommendations);
    const suggestions: string[] = [];
    
    const total = recommendations.length;
    if (total === 0) {
      return { isBalanced: true, distribution, suggestions };
    }
    
    const criticalPercent = (distribution.critical / total) * 100;
    const highPercent = (distribution.high / total) * 100;
    const mediumPercent = (distribution.medium / total) * 100;
    const lowPercent = (distribution.low / total) * 100;
    
    let isBalanced = true;
    
    if (criticalPercent > 30) {
      isBalanced = false;
      suggestions.push('Too many critical recommendations. Review if all are truly critical.');
    }
    
    if (highPercent > 50) {
      isBalanced = false;
      suggestions.push('High concentration of high-priority items. Consider more granular prioritization.');
    }
    
    if (lowPercent > 60) {
      isBalanced = false;
      suggestions.push('Many low-priority items. Consider if some should be removed or deferred.');
    }
    
    if (criticalPercent === 0 && total > 10) {
      suggestions.push('No critical recommendations with many total items. Review if priorities are accurate.');
    }
    
    return {
      isBalanced,
      distribution: {
        critical: distribution.critical,
        high: distribution.high,
        medium: distribution.medium,
        low: distribution.low,
        criticalPercent: parseFloat(criticalPercent.toFixed(1)),
        highPercent: parseFloat(highPercent.toFixed(1)),
        mediumPercent: parseFloat(mediumPercent.toFixed(1)),
        lowPercent: parseFloat(lowPercent.toFixed(1)),
      },
      suggestions,
    };
  }
  
  // Helper methods
  
  private static isValidSource(source: string): boolean {
    const validSources = ['performance', 'database', 'error', 'resource', 'ml', 'security', 'business'];
    return validSources.includes(source);
  }
  
  private static isValidType(type: string): boolean {
    const validTypes = ['optimization', 'scaling', 'caching', 'refactoring', 'security', 'configuration'];
    return validTypes.includes(type);
  }
  
  private static isValidImpact(impact: string): boolean {
    const validImpacts = ['critical', 'high', 'medium', 'low'];
    return validImpacts.includes(impact);
  }
  
  private static isValidEffort(effort: string): boolean {
    const validEfforts = ['low', 'medium', 'high'];
    return validEfforts.includes(effort);
  }
  
  private static clampPriority(priority: any): number {
    const num = typeof priority === 'number' ? priority : 50;
    return Math.max(0, Math.min(100, num));
  }
  
  private static groupByPriority(recommendations: UnifiedRecommendation[]): {
    critical: number;
    high: number;
    medium: number;
    low: number;
  } {
    let critical = 0, high = 0, medium = 0, low = 0;
    
    recommendations.forEach(rec => {
      if (rec.priority >= 90) critical++;
      else if (rec.priority >= 70) high++;
      else if (rec.priority >= 40) medium++;
      else low++;
    });
    
    return { critical, high, medium, low };
  }
}

export default RecommendationValidator;