/**
 * Error handling utilities for TypeScript strict mode compliance
 */

/**
 * Type guard to check if a value is an Error instance
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Type guard to check if error has a message property
 */
export function hasMessage(error: unknown): error is { message: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as any).message === 'string'
  );
}

/**
 * Type guard to check if error has a code property
 */
export function hasCode(error: unknown): error is { code: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    typeof (error as any).code === 'string'
  );
}

/**
 * Safe error message extraction
 */
export function getErrorMessage(error: unknown): string {
  if (isError(error)) {
    return error.message;
  }
  if (hasMessage(error)) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Unknown error occurred';
}

/**
 * Safe error stack extraction
 */
export function getErrorStack(error: unknown): string | undefined {
  if (isError(error)) {
    return error.stack;
  }
  return undefined;
}

/**
 * Safe error code extraction
 */
export function getErrorCode(error: unknown): string | undefined {
  if (hasCode(error)) {
    return error.code;
  }
  if (isError(error) && 'code' in error) {
    return (error as any).code;
  }
  return undefined;
}

/**
 * Convert unknown error to Error instance
 */
export function toError(error: unknown): Error {
  if (isError(error)) {
    return error;
  }
  if (hasMessage(error)) {
    return new Error(error.message);
  }
  if (typeof error === 'string') {
    return new Error(error);
  }
  return new Error('Unknown error occurred');
}

/**
 * Format error for logging
 */
export function formatErrorForLogging(error: unknown): Record<string, unknown> {
  const errorMessage = getErrorMessage(error);
  const errorStack = getErrorStack(error);
  const errorCode = getErrorCode(error);
  
  const formatted: Record<string, unknown> = {
    message: errorMessage,
  };
  
  if (errorStack) {
    formatted.stack = errorStack;
  }
  
  if (errorCode) {
    formatted.code = errorCode;
  }
  
  // Include additional properties if error is an object
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;
    for (const key of Object.keys(errorObj)) {
      if (!['message', 'stack', 'code'].includes(key)) {
        formatted[key] = errorObj[key];
      }
    }
  }
  
  return formatted;
}