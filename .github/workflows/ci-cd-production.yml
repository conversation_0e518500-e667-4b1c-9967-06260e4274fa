name: CI/CD Production Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to production'
        required: false
        default: 'false'

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_PREFIX: ${{ github.repository_owner }}/spheroseg

jobs:
  # Security scanning job
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner (repository)
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'

      - name: Upload Trivy scan results to GitHub Security
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --all-projects --severity-threshold=high

      - name: Run GitLeaks (secret scanning)
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Code quality checks
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run Prettier check
        run: npm run format:check

      - name: Check TypeScript
        run: npm run build

  # Test Frontend
  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: [security-scan, code-quality]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd packages/frontend
          npm ci

      - name: Run unit tests
        run: |
          cd packages/frontend
          npm run test:ci

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/frontend/coverage/lcov.info
          flags: frontend

  # Test Backend
  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: [security-scan, code-quality]
    services:
      postgres:
        image: postgres:14-alpine
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: spheroseg_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd packages/backend
          npm ci

      - name: Run database migrations
        env:
          DATABASE_URL: postgresql://postgres:testpass@localhost:5432/spheroseg_test
        run: |
          cd packages/backend
          npm run migrate

      - name: Run unit tests
        env:
          DATABASE_URL: postgresql://postgres:testpass@localhost:5432/spheroseg_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret-key-for-ci-testing-only
          NODE_ENV: test
        run: |
          cd packages/backend
          npm run test:ci

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/backend/coverage/lcov.info
          flags: backend

  # Test ML Service
  test-ml:
    name: ML Service Tests
    runs-on: ubuntu-latest
    needs: [security-scan]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install dependencies
        run: |
          cd packages/ml
          pip install -r requirements.txt
          pip install pytest pytest-cov

      - name: Run tests
        run: |
          cd packages/ml
          python -m pytest tests/ --cov=. --cov-report=xml

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/ml/coverage.xml
          flags: ml

  # Build Docker images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend, test-ml]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    strategy:
      matrix:
        service: [frontend, backend, ml]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_PREFIX }}-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./packages/${{ matrix.service }}/Dockerfile${{ matrix.service == 'frontend' && '.prod' || '' }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Run Trivy vulnerability scanner (container)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_PREFIX }}-${{ matrix.service }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-container-${{ matrix.service }}.sarif'

      - name: Upload container scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-container-${{ matrix.service }}.sarif'

  # Integration tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Start services
        run: |
          export IMAGE_TAG="${{ github.sha }}"
          docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d
          sleep 30  # Wait for services to start

      - name: Check service health
        run: |
          curl -f http://localhost:5001/api/health || exit 1
          curl -f http://localhost:5002/health || exit 1
          curl -f http://localhost:3000 || exit 1

      - name: Run E2E tests
        run: |
          cd e2e
          npm ci
          npm run test:ci

      - name: Collect logs on failure
        if: failure()
        run: |
          docker-compose logs > docker-logs.txt
          echo "::group::Docker Logs"
          cat docker-logs.txt
          echo "::endgroup::"

      - name: Stop services
        if: always()
        run: docker-compose down

  # Deploy to production
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-tests]
    if: |
      github.event_name == 'push' && 
      github.ref == 'refs/heads/main' && 
      (github.event.inputs.deploy == 'true' || contains(github.event.head_commit.message, '[deploy]'))
    environment:
      name: production
      url: https://spherosegapp.utia.cas.cz
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /opt/spheroseg
            git pull origin main
            
            # Create secrets if not exists
            ./scripts/create-docker-secrets.sh
            
            # Pull latest images
            docker-compose -f docker-compose.yml -f docker-compose.production.yml pull
            
            # Deploy with zero downtime
            docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d --no-deps --scale backend=2 backend
            sleep 30
            docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d --no-deps frontend-prod nginx-prod
            
            # Clean up old images
            docker image prune -f

      - name: Verify deployment
        run: |
          sleep 60
          curl -f https://spherosegapp.utia.cas.cz/api/health || exit 1

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          text: |
            Production deployment ${{ job.status }}
            Commit: ${{ github.event.head_commit.message }}
            Author: ${{ github.event.head_commit.author.name }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

  # Cleanup old images
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    needs: [deploy]
    if: success()
    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v4
        with:
          package-name: 'spheroseg-frontend'
          package-type: 'container'
          min-versions-to-keep: 5
          delete-only-pre-release-versions: 'false'

      - name: Delete old backend images
        uses: actions/delete-package-versions@v4
        with:
          package-name: 'spheroseg-backend'
          package-type: 'container'
          min-versions-to-keep: 5

      - name: Delete old ML images
        uses: actions/delete-package-versions@v4
        with:
          package-name: 'spheroseg-ml'
          package-type: 'container'
          min-versions-to-keep: 5