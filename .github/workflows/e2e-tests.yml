name: E2E Tests

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

jobs:
  e2e-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd packages/frontend
          npm ci
          
      - name: Install Playwright Browsers
        run: |
          cd packages/frontend
          npx playwright install --with-deps ${{ matrix.browser }}
          
      - name: Start Docker services
        run: |
          docker-compose --profile dev up -d
          sleep 30 # Wait for services to start
          
      - name: Run E2E tests
        run: |
          cd packages/frontend
          npx playwright test --project=${{ matrix.browser }}
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-results-${{ matrix.browser }}
          path: |
            packages/frontend/playwright-report/
            packages/frontend/test-results/
          retention-days: 30
          
      - name: Upload test videos
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-videos-${{ matrix.browser }}
          path: packages/frontend/test-results/**/video.webm
          retention-days: 7
          
      - name: Stop Docker services
        if: always()
        run: docker-compose down
        
  e2e-report:
    needs: e2e-tests
    if: always()
    runs-on: ubuntu-latest
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        
      - name: Merge test results
        run: |
          mkdir -p merged-results
          cp -r playwright-results-*/* merged-results/
          
      - name: Deploy to GitHub Pages
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./merged-results
          destination_dir: e2e-report