name: Import Validation

on:
  push:
    branches: [main, dev]
    paths:
      - 'packages/frontend/**/*.ts'
      - 'packages/frontend/**/*.tsx'
      - 'packages/frontend/**/*.js'
      - 'packages/frontend/**/*.jsx'
  pull_request:
    branches: [main, dev]
    paths:
      - 'packages/frontend/**/*.ts'
      - 'packages/frontend/**/*.tsx'
      - 'packages/frontend/**/*.js'
      - 'packages/frontend/**/*.jsx'

jobs:
  validate-imports:
    name: Validate Imports
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd packages/frontend && npm ci
          
      - name: Run import validation
        run: |
          cd packages/frontend
          node scripts/check-imports.js
          
      - name: Run ESLint import rules
        run: |
          cd packages/frontend
          npx eslint src --ext .ts,.tsx,.js,.jsx --rule "import/no-unresolved: error" --rule "import/named: error" --rule "import/default: error" --rule "import/namespace: error" --rule "local-rules/ensure-lazy-imports: error"
          
      - name: Check for circular dependencies
        run: |
          cd packages/frontend
          npx madge --circular --extensions ts,tsx src
          
      - name: Generate import report
        if: always()
        run: |
          cd packages/frontend
          echo "## Import Analysis Report" > import-report.md
          echo "" >> import-report.md
          
          echo "### File Count by Extension" >> import-report.md
          find src -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) | awk -F. '{print $NF}' | sort | uniq -c >> import-report.md
          echo "" >> import-report.md
          
          echo "### Import Statistics" >> import-report.md
          echo "Total imports: $(grep -r "^import\|^export.*from" src --include="*.ts" --include="*.tsx" | wc -l)" >> import-report.md
          echo "Lazy imports: $(grep -r "lazy(" src --include="*.ts" --include="*.tsx" | wc -l)" >> import-report.md
          echo "Dynamic imports: $(grep -r "import(" src --include="*.ts" --include="*.tsx" | wc -l)" >> import-report.md
          echo "" >> import-report.md
          
          echo "### Most Imported Modules" >> import-report.md
          grep -r "from ['\"]" src --include="*.ts" --include="*.tsx" | sed "s/.*from ['\"]\\([^'\"]*\\)['\"].*/\\1/" | sort | uniq -c | sort -nr | head -20 >> import-report.md
          
      - name: Upload import report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: import-analysis-report
          path: packages/frontend/import-report.md
          
      - name: Comment PR with import issues
        if: failure() && github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('packages/frontend/import-report.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## ❌ Import Validation Failed\n\n${report}\n\n### Required Actions:\n- Fix all import errors\n- Ensure lazy loading is used for page components\n- Resolve any circular dependencies\n\nRun \`npm run lint:fix\` locally to auto-fix some issues.`
            });

  validate-bundle-size:
    name: Validate Bundle Size Impact
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd packages/frontend && npm ci
          
      - name: Build and analyze bundle
        run: |
          cd packages/frontend
          npm run build
          
          # Generate bundle analysis
          npx webpack-bundle-analyzer dist/stats.json dist -m static -r bundle-report.html -O
          
      - name: Check bundle size limits
        run: |
          cd packages/frontend
          
          # Define size limits (in KB)
          MAX_MAIN_BUNDLE=500
          MAX_VENDOR_BUNDLE=800
          MAX_TOTAL_SIZE=2000
          
          # Get actual sizes
          MAIN_SIZE=$(find dist -name "index*.js" -exec du -k {} \; | awk '{sum += $1} END {print sum}')
          VENDOR_SIZE=$(find dist -name "vendor*.js" -exec du -k {} \; | awk '{sum += $1} END {print sum}')
          TOTAL_SIZE=$(du -sk dist | awk '{print $1}')
          
          echo "Bundle sizes (KB):"
          echo "Main: $MAIN_SIZE (limit: $MAX_MAIN_BUNDLE)"
          echo "Vendor: $VENDOR_SIZE (limit: $MAX_VENDOR_BUNDLE)"
          echo "Total: $TOTAL_SIZE (limit: $MAX_TOTAL_SIZE)"
          
          # Check limits
          if [ "$MAIN_SIZE" -gt "$MAX_MAIN_BUNDLE" ]; then
            echo "❌ Main bundle exceeds limit!"
            exit 1
          fi
          
          if [ "$VENDOR_SIZE" -gt "$MAX_VENDOR_BUNDLE" ]; then
            echo "❌ Vendor bundle exceeds limit!"
            exit 1
          fi
          
          if [ "$TOTAL_SIZE" -gt "$MAX_TOTAL_SIZE" ]; then
            echo "❌ Total bundle size exceeds limit!"
            exit 1
          fi
          
          echo "✅ All bundle sizes within limits"
          
      - name: Upload bundle report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis-report
          path: packages/frontend/bundle-report.html