name: Pull Request Checks

on:
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Quick checks that should pass before other jobs
  pre-checks:
    name: Pre-checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check for merge conflicts
        run: |
          if git merge-tree $(git merge-base HEAD ${{ github.base_ref }}) HEAD ${{ github.base_ref }} | grep -q "<<<<<<< "; then
            echo "::error::Merge conflicts detected"
            exit 1
          fi

      - name: Check commit messages
        uses: webiny/action-conventional-commits@v1.1.0

      - name: Check file sizes
        run: |
          # Check for large files (>5MB)
          large_files=$(find . -type f -size +5M | grep -v -E '(\.git|node_modules|venv|dist|build)')
          if [ -n "$large_files" ]; then
            echo "::error::Large files detected (>5MB):"
            echo "$large_files"
            exit 1
          fi

  # Lint and format checks
  lint:
    name: Lint and Format
    runs-on: ubuntu-latest
    needs: pre-checks
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check code formatting
        run: |
          npm run format:check
          if [ $? -ne 0 ]; then
            echo "::error::Code is not properly formatted. Run 'npm run format' locally."
            exit 1
          fi

      - name: Run ESLint
        run: |
          npm run lint
          if [ $? -ne 0 ]; then
            echo "::error::ESLint errors found. Run 'npm run lint:fix' locally."
            exit 1
          fi

      - name: Check TypeScript
        run: npm run build

  # Quick security check
  security-check:
    name: Security Check
    runs-on: ubuntu-latest
    needs: pre-checks
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check dependencies
        run: |
          npm audit --audit-level=high
          if [ $? -ne 0 ]; then
            echo "::warning::High severity vulnerabilities found in dependencies"
          fi

  # Unit tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: [lint, security-check]
    strategy:
      matrix:
        package: [frontend, backend]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd packages/${{ matrix.package }}
          npm ci

      - name: Run tests
        run: |
          cd packages/${{ matrix.package }}
          npm run test:ci -- --coverage

      - name: Check coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/${{ matrix.package }}/coverage/lcov.info
          flags: ${{ matrix.package }}
          fail_ci_if_error: false

  # Build check
  build-check:
    name: Build Check
    runs-on: ubuntu-latest
    needs: [lint, security-check]
    strategy:
      matrix:
        service: [frontend, backend, ml]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./packages/${{ matrix.service }}/Dockerfile${{ matrix.service == 'frontend' && '.prod' || '' }}
          push: false
          tags: spheroseg-${{ matrix.service }}:pr-${{ github.event.pull_request.number }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # PR Summary
  pr-summary:
    name: PR Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, build-check]
    if: always()
    steps:
      - name: Generate PR comment
        uses: actions/github-script@v6
        with:
          script: |
            const jobResults = {
              'Pre-checks': '${{ needs.pre-checks.result }}',
              'Lint': '${{ needs.lint.result }}',
              'Security': '${{ needs.security-check.result }}',
              'Unit Tests': '${{ needs.unit-tests.result }}',
              'Build': '${{ needs.build-check.result }}'
            };

            let comment = '## Pull Request Check Results\n\n';
            comment += '| Check | Status |\n';
            comment += '|-------|--------|\n';

            let allPassed = true;
            for (const [job, result] of Object.entries(jobResults)) {
              const emoji = result === 'success' ? '✅' : result === 'failure' ? '❌' : '⏭️';
              comment += `| ${job} | ${emoji} ${result} |\n`;
              if (result === 'failure') allPassed = false;
            }

            comment += '\n';
            if (allPassed) {
              comment += '✅ **All checks passed!** This PR is ready for review.\n';
            } else {
              comment += '❌ **Some checks failed.** Please fix the issues before merging.\n';
            }

            // Find existing comment
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const botComment = comments.find(comment => 
              comment.user.type === 'Bot' && 
              comment.body.includes('Pull Request Check Results')
            );

            if (botComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: comment
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: comment
              });
            }