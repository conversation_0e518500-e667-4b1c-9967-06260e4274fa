#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Pre-commit hook for Spheroseg
# Runs code quality checks before allowing commits with enhanced error handling and performance optimization

# Enable error reporting and exit on failure
set -e
set -o pipefail

# Enhanced colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Performance tracking
start_time=$(node -e "console.log(Date.now())")

# Header with enhanced formatting
echo "${BOLD}${BLUE}🔍 Spheroseg Pre-commit Quality Gates${NC}"
echo "${CYAN}═══════════════════════════════════════${NC}"

# Enhanced function to log with timestamp and context
log_with_time() {
    local level="${2:-INFO}"
    local timestamp=$(date +'%H:%M:%S')
    case $level in
        "SUCCESS") echo "${GREEN}[${timestamp}] ✅${NC} $1" ;;
        "WARNING") echo "${YELLOW}[${timestamp}] ⚠️${NC} $1" ;;
        "ERROR")   echo "${RED}[${timestamp}] ❌${NC} $1" ;;
        "INFO")    echo "${BLUE}[${timestamp}] 📋${NC} $1" ;;
        *)         echo "${BLUE}[${timestamp}]${NC} $1" ;;
    esac
}

# Enhanced function to handle errors with context and recovery suggestions
handle_error() {
    local exit_code=$?
    local line_number=$1
    local error_context="${2:-general}"
    
    echo ""
    echo "${RED}${BOLD}❌ Pre-commit Quality Gate Failed${NC}"
    echo "${CYAN}═══════════════════════════════════════${NC}"
    echo "${RED}Exit code: $exit_code | Line: $line_number | Context: $error_context${NC}"
    echo ""
    
    echo "${YELLOW}${BOLD}🔧 Quick Recovery Options:${NC}"
    echo "${YELLOW}  1.${NC} Auto-fix common issues: ${CYAN}npm run code:fix${NC}"
    echo "${YELLOW}  2.${NC} Check detailed issues: ${CYAN}npm run code:check${NC}"
    echo "${YELLOW}  3.${NC} View specific errors: ${CYAN}Check output above for file locations${NC}"
    echo "${YELLOW}  4.${NC} Emergency bypass: ${CYAN}git commit --no-verify${NC} ${RED}(use sparingly!)${NC}"
    echo ""
    
    echo "${BLUE}${BOLD}📚 Documentation:${NC}"
    echo "${BLUE}  •${NC} Pre-commit hooks: ${CYAN}docs/development/pre-commit-hooks.md${NC}"
    echo "${BLUE}  •${NC} Code quality guide: ${CYAN}docs/development/code-quality.md${NC}"
    echo "${BLUE}  •${NC} Python tools setup: ${CYAN}docs/development/python-tools-setup.md${NC}"
    echo ""
    
    # Enhanced performance logging with error context
    if [ -n "$start_time" ]; then
        end_time=$(node -e "console.log(Date.now())")
        duration=$((end_time - start_time))
        node scripts/log-hook-performance.js pre-commit $duration 0 "failed:$error_context" 2>/dev/null || true
    fi
    
    exit $exit_code
}

# Set up enhanced error trap with context
trap 'handle_error $LINENO "environment"' ERR

# Smart environment detection and optimization
log_with_time "Detecting environment and optimizing..." "INFO"

# Environment-specific optimizations
if [ "${CI:-false}" = "true" ]; then
    log_with_time "CI environment detected - applying CI optimizations" "INFO"
    export NODE_OPTIONS="${NODE_OPTIONS} --max-old-space-size=4096"
    export LINT_STAGED_CONCURRENCY=2
    export SKIP_SLOW_CHECKS=true
elif [ "$(uname)" = "Darwin" ]; then
    log_with_time "macOS detected - optimizing for Apple Silicon" "INFO"
    export NODE_OPTIONS="${NODE_OPTIONS} --max-old-space-size=2048"
    export LINT_STAGED_CONCURRENCY=auto
else
    log_with_time "Linux environment - using standard optimizations" "INFO"
    export LINT_STAGED_CONCURRENCY=auto
fi

# Progressive tool availability checking
log_with_time "Verifying required tools..." "INFO"

check_tool() {
    local tool=$1
    local install_hint=$2
    if ! command -v "$tool" >/dev/null 2>&1; then
        log_with_time "$tool not found. $install_hint" "ERROR"
        exit 1
    else
        log_with_time "$tool available" "SUCCESS"
    fi
}

check_tool "node" "Please install Node.js from https://nodejs.org/"
check_tool "npx" "Please update npm: npm install -g npm@latest"

# Enhanced file change detection
staged_files=$(git diff --cached --name-only --diff-filter=ACMR 2>/dev/null | wc -l)
log_with_time "Processing $staged_files staged files" "INFO"

if [ "$staged_files" -eq 0 ]; then
    log_with_time "No staged files found - skipping checks" "WARNING"
    exit 0
fi

# Performance-optimized lint-staged execution
trap 'handle_error $LINENO "lint-staged"' ERR

log_with_time "Executing quality checks on changed files..." "INFO"
lint_start=$(node -e "console.log(Date.now())")

# Enhanced timeout handling with graceful degradation
if command -v timeout >/dev/null 2>&1; then
    # Linux timeout command
    timeout 300 npx lint-staged || {
        log_with_time "Lint-staged timeout exceeded (5 min limit)" "ERROR"
        exit 1
    }
elif command -v gtimeout >/dev/null 2>&1; then
    # macOS with GNU coreutils
    gtimeout 300 npx lint-staged || {
        log_with_time "Lint-staged timeout exceeded (5 min limit)" "ERROR"
        exit 1
    }
else
    # Fallback without timeout (macOS default)
    log_with_time "Running without timeout protection" "WARNING"
    npx lint-staged
fi

lint_end=$(node -e "console.log(Date.now())")
lint_duration=$((lint_end - lint_start))

# Calculate comprehensive performance metrics
end_time=$(node -e "console.log(Date.now())")
total_duration=$((end_time - start_time))
total_seconds=$((total_duration / 1000))
lint_seconds=$((lint_duration / 1000))

# Reset error trap for final reporting
trap - ERR

echo ""
echo "${CYAN}═══════════════════════════════════════${NC}"
echo "${GREEN}${BOLD}✅ All Quality Gates Passed!${NC}"
echo "${CYAN}═══════════════════════════════════════${NC}"

# Enhanced performance reporting
echo "${BLUE}${BOLD}📊 Performance Summary:${NC}"
echo "${BLUE}  • Total execution time: ${NC}${total_seconds}s (${total_duration}ms)"
echo "${BLUE}  • Lint-staged duration: ${NC}${lint_seconds}s (${lint_duration}ms)"
echo "${BLUE}  • Files processed: ${NC}$staged_files"
echo "${BLUE}  • Average per file: ${NC}$((total_duration / staged_files))ms"

# Intelligent performance analysis and recommendations
if [ $total_duration -gt 60000 ]; then
    echo ""
    echo "${YELLOW}${BOLD}⚠️  Performance Alert: Execution exceeded 60s${NC}"
    echo "${YELLOW}  Recommendations:${NC}"
    echo "${YELLOW}  • Consider breaking large commits into smaller ones${NC}"
    echo "${YELLOW}  • Run 'npm run cache:clean' to clear stale caches${NC}"
    echo "${YELLOW}  • Check 'npm run cache:status' for cache efficiency${NC}"
elif [ $total_duration -gt 30000 ]; then
    echo ""
    echo "${YELLOW}${BOLD}⚠️  Large changeset detected (>30s execution)${NC}"
    echo "${YELLOW}  • Consider staging files incrementally with 'git add -p'${NC}"
    echo "${YELLOW}  • Use 'git add <specific-files>' for targeted commits${NC}"
elif [ $total_duration -lt 5000 ]; then
    echo ""
    echo "${GREEN}${BOLD}🚀 Excellent performance! (< 5s execution)${NC}"
    echo "${GREEN}  • Caching is working effectively${NC}"
    echo "${GREEN}  • Optimal commit size detected${NC}"
fi

# Cache efficiency reporting
cache_hit_info=""
if [ -d ".cache" ]; then
    cache_size=$(du -sh .cache 2>/dev/null | cut -f1 2>/dev/null || echo "unknown")
    cache_hit_info=" | Cache: ${cache_size}"
fi

echo "${BLUE}  • Environment: ${NC}$(uname -s) | Node: $(node --version)${cache_hit_info}"

# Enhanced performance logging with detailed metrics
log_performance_data() {
    if command -v node >/dev/null 2>&1; then
        node scripts/log-hook-performance.js pre-commit \
            "$total_duration" "$lint_duration" "success" \
            "$staged_files" "$(uname -s)" 2>/dev/null || true
    fi
}

log_performance_data

echo ""
echo "${GREEN}${BOLD}🎉 Ready to commit! Your code meets all quality standards.${NC}"
echo "${CYAN}═══════════════════════════════════════${NC}"