# Production Deployment Guide

## Overview

This guide covers the production deployment process for SpherosegV4 backend.

## Current Status

✅ **Update (2025-07-21)**: All TypeScript compilation errors have been resolved. The standard build process now works correctly.

### Build Status
- **Total Errors**: 0 (all fixed)
- **Build Process**: Standard `npm run build` now works
- **Deployment**: Normal deployment process can be used

## Deployment Process

### 1. Prerequisites

- Node.js 18.x or higher
- PostgreSQL database
- Redis server
- RabbitMQ (optional, for queue processing)

### 2. Environment Setup

Create a `.env.production` file in `/home/<USER>/spheroseg/packages/backend/`:

```bash
# Database
DATABASE_URL=************************************/spheroseg

# Server
NODE_ENV=production
PORT=5001

# JWT
JWT_SECRET=your-secure-jwt-secret
JWT_REFRESH_SECRET=your-secure-refresh-secret

# Redis
REDIS_URL=redis://localhost:6379

# RabbitMQ (optional)
RABBITMQ_URL=amqp://localhost

# CORS
ALLOWED_ORIGINS=https://your-domain.com

# Storage
UPLOAD_DIR=/var/spheroseg/uploads
STATIC_DIR=/var/spheroseg/static

# Email (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

### 3. Deployment Methods

#### Method A: Standard Build Process (Recommended)

Now that TypeScript errors are fixed, use the standard build process:

```bash
cd /home/<USER>/spheroseg/packages/backend

# Build the application
npm run build

# Start the application
NODE_ENV=production node dist/src/server.js
```

#### Method B: Docker Deployment (Alternative)

Use Docker Compose for a containerized deployment:

```bash
cd /home/<USER>/spheroseg

# Production mode
docker-compose --profile prod up -d
```

### 4. Post-Deployment

#### Verify Services

1. **Backend API**: `curl http://localhost:5001/api/health`
2. **Database**: Check connections in logs
3. **Redis**: `redis-cli ping`
4. **File uploads**: Test image upload functionality

#### Setup Monitoring

1. **Logs**: Check `/home/<USER>/spheroseg/packages/backend/logs/`
2. **PM2 (if used)**: `pm2 status spheroseg-backend`
3. **Performance**: Monitor at `/api/performance/metrics`

#### Setup Nginx (Recommended)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /socket.io {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
    }
}
```

### 5. Troubleshooting

#### Common Issues

1. **Build Issues**
   - Ensure all dependencies are installed: `npm install`
   - Clear dist directory if needed: `rm -rf dist`

2. **Database Connection Errors**
   - Verify DATABASE_URL is correct
   - Check PostgreSQL is running
   - Ensure database exists: `createdb spheroseg`

3. **Redis Connection Errors**
   - Verify Redis is running: `redis-cli ping`
   - Check REDIS_URL in .env.production

4. **File Upload Errors**
   - Ensure upload directories exist and are writable
   - Check disk space

#### Logs Location

- Application logs: `stdout` (or PM2 logs if using PM2)
- Error logs: Check console output
- Performance logs: `/api/performance/metrics`

### 6. Rollback Procedure

If deployment fails:

1. Stop the service: `pm2 stop spheroseg-backend` or kill the node process
2. Restore previous build: `rm -rf dist && git checkout dist` (if versioned)
3. Restart with previous version

### 7. Future Improvements

- [x] Fix all TypeScript compilation errors ✅ (Completed 2025-07-21)
- [ ] Setup CI/CD pipeline
- [ ] Add health check endpoints
- [ ] Implement zero-downtime deployments
- [ ] Add automated backup procedures

## Security Checklist

- [x] Environment variables secured
- [x] JWT secrets are strong and unique
- [x] CORS configured for production domain
- [x] Rate limiting enabled
- [x] SQL injection protection via parameterized queries
- [x] XSS protection headers configured
- [ ] SSL/TLS certificate installed
- [ ] Security headers configured in Nginx

## Performance Optimization

- [x] Database indexes created
- [x] Redis caching enabled
- [x] Static file caching configured
- [x] Gzip compression enabled
- [ ] CDN configured for static assets
- [ ] Database connection pooling optimized

---

**Note**: The standard `npm run build` process now works correctly after all TypeScript errors were resolved on 2025-07-21.