# SpherosegV4 Production Deployment Guide

This guide provides step-by-step instructions for deploying SpherosegV4 to production.

## Prerequisites

- Linux server (Ubuntu 20.04+ or similar)
- Docker 20.10+ and Docker Compose 1.29+
- Domain name with DNS configured
- SSL certificates (Let's Encrypt recommended)
- At least 8GB RAM and 4 CPU cores
- 100GB+ storage for images and database
- Email service for notifications (SMTP)

## Quick Start

```bash
# Clone the repository
git clone https://github.com/your-org/spheroseg.git
cd spheroseg

# Run production setup script
./scripts/setup-production-env.sh

# Deploy
./deploy.sh
```

## Detailed Deployment Steps

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install other dependencies
sudo apt install -y git openssl
```

### 2. Clone and Configure

```bash
# Clone repository
git clone https://github.com/your-org/spheroseg.git
cd spheroseg

# Create production environment
./scripts/setup-production-env.sh
```

This script will:
- Generate secure secrets (JWT, database passwords, etc.)
- Create Docker secrets
- Set up directory structure
- Configure domain settings

### 3. SSL Certificate Setup

#### Option A: Let's Encrypt (Recommended)

```bash
./scripts/ssl/setup-ssl-production.sh yourdomain.com
```

Follow the prompts to set up Let's Encrypt certificates.

#### Option B: Custom Certificates

Place your certificates in the `./ssl` directory:
- `ssl/yourdomain.com.crt` (certificate)
- `ssl/yourdomain.com.key` (private key)
- `ssl/chain.pem` (certificate chain, optional)

### 4. Environment Configuration

Review and update `.env` file:

```bash
# Critical settings to verify
DATABASE_URL=************************************************/spheroseg
JWT_SECRET=GENERATED_64_CHAR_SECRET
REDIS_PASSWORD=GENERATED_PASSWORD

# Email settings (required for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Domain settings
APP_URL=https://yourdomain.com
VITE_API_URL=https://api.yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Optional monitoring
SENTRY_DSN=your-sentry-dsn
```

### 5. Deploy Application

```bash
# Run deployment script
./deploy.sh

# Or manually:
docker-compose -f docker-compose.prod.yml up -d
```

### 6. Post-Deployment Setup

#### Create Admin User

```bash
docker-compose -f docker-compose.prod.yml exec backend node scripts/create-admin-user.js <EMAIL> yourpassword
```

#### Run Database Migrations

```bash
docker-compose -f docker-compose.prod.yml exec backend npm run db:migrate
```

#### Verify Deployment

```bash
# Check service health
curl https://yourdomain.com/api/health

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Check service status
docker-compose -f docker-compose.prod.yml ps
```

## Production Configuration

### Resource Limits

The production Docker Compose file sets resource limits for each service:

- **Database**: 2GB RAM, 2 CPUs
- **Backend**: 1GB RAM, 2 CPUs (2 replicas)
- **ML Service**: 4GB RAM, 4 CPUs, 1 GPU
- **Frontend**: 256MB RAM, 0.5 CPUs
- **Redis**: 512MB RAM, 1 CPU

Adjust these in `docker-compose.prod.yml` based on your server capacity.

### Performance Tuning

#### Database Optimization

```sql
-- Connect to database
docker-compose -f docker-compose.prod.yml exec db psql -U postgres -d spheroseg

-- Verify indexes
\di

-- Check slow queries
SELECT query, calls, mean_exec_time
FROM pg_stat_statements
ORDER BY mean_exec_time DESC
LIMIT 10;
```

#### Redis Configuration

Redis is configured with:
- Persistence enabled (AOF)
- Password protection
- Memory limit of 512MB

#### NGINX Optimization

- Gzip compression enabled
- Static asset caching (1 year)
- Rate limiting for API endpoints
- SSL session caching

### Monitoring

#### Built-in Monitoring

Access performance metrics:
```bash
curl https://api.yourdomain.com/performance/metrics
```

#### Log Management

Logs are stored in `./logs` directory:
- `nginx/access.log` - HTTP access logs
- `nginx/error.log` - NGINX errors
- `backend/app.log` - Application logs

#### Health Checks

- Frontend: `https://yourdomain.com/health`
- API: `https://api.yourdomain.com/health`
- Database: Port 5432
- Redis: Port 6379

### Backup and Recovery

#### Automated Backups

The deployment includes an automated backup service that:
- Runs daily at 2 AM
- Keeps backups for 30 days
- Optionally uploads to S3

Configure S3 backup:
```bash
# Add to .env
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
```

#### Manual Backup

```bash
# Database backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U postgres spheroseg > backup-$(date +%Y%m%d).sql

# Full application backup
tar -czf spheroseg-backup-$(date +%Y%m%d).tar.gz uploads/ backup/
```

#### Recovery

```bash
# Restore database
docker-compose -f docker-compose.prod.yml exec -T db psql -U postgres spheroseg < backup.sql

# Restore uploads
tar -xzf spheroseg-backup.tar.gz
```

### Security Best Practices

1. **Secrets Management**
   - Use Docker secrets for sensitive data
   - Rotate secrets regularly using `scripts/rotate-secrets.ts`
   - Never commit secrets to version control

2. **Network Security**
   - Use firewall (ufw) to restrict ports
   - Only expose ports 80 and 443
   - Use internal Docker network for service communication

3. **SSL/TLS**
   - Use Let's Encrypt for free certificates
   - Enable HSTS headers
   - Configure strong cipher suites

4. **Updates**
   - Regularly update Docker images
   - Apply security patches promptly
   - Monitor dependencies for vulnerabilities

### Scaling

#### Horizontal Scaling

Backend service is configured with 2 replicas by default. Increase replicas:

```yaml
# In docker-compose.prod.yml
backend:
  deploy:
    replicas: 4  # Increase as needed
```

#### ML Service Scaling

For ML service scaling, consider:
- Using multiple GPU nodes
- Implementing queue-based processing
- Load balancing between ML instances

### Troubleshooting

#### Common Issues

1. **Health check failing**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs backend
   
   # Verify database connection
   docker-compose -f docker-compose.prod.yml exec backend npm run db:test
   ```

2. **SSL certificate issues**
   ```bash
   # Check certificate validity
   openssl x509 -in ssl/fullchain.pem -text -noout
   
   # Renew Let's Encrypt
   docker-compose -f docker-compose.prod.yml run --rm certbot renew
   ```

3. **Performance issues**
   ```bash
   # Check resource usage
   docker stats
   
   # View performance metrics
   curl https://api.yourdomain.com/performance/metrics
   ```

4. **Database connection errors**
   ```bash
   # Check database status
   docker-compose -f docker-compose.prod.yml exec db pg_isready
   
   # View database logs
   docker-compose -f docker-compose.prod.yml logs db
   ```

### Maintenance

#### Regular Tasks

- **Daily**: Check logs and health endpoints
- **Weekly**: Review performance metrics, check disk space
- **Monthly**: Update dependencies, rotate logs, review security
- **Quarterly**: Full backup test, security audit

#### Update Procedure

```bash
# Pull latest changes
git pull origin main

# Rebuild and deploy
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend npm run db:migrate
```

## Support

For issues or questions:
- Check logs: `docker-compose -f docker-compose.prod.yml logs`
- Review documentation: `/docs` directory
- Contact: <EMAIL>